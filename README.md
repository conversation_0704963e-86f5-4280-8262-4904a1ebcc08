# Zeeguros - Reverse Auction Platform for Insurance

![Build Status](https://img.shields.io/badge/build-passing-brightgreen)
[![Version](https://img.shields.io/badge/version-0.1.0-blue)](./package.json)
[![Architecture](https://img.shields.io/badge/architecture-screaming-green)](./docs/architecture.md)
[![TypeScript](https://img.shields.io/badge/typescript-100%25-blue)](./tsconfig.json)

Zeeguros is a reverse auction platform for insurance policies, where users upload their current policy and insurance brokers compete to offer better coverage or price.

## Description

Zeeguros is a comprehensive web application designed to modernize the insurance industry. It provides a robust set of tools for users to manage their car insurance policies, for brokers to manage their client portfolios, and for the platform to leverage cutting-edge AI for data automation. The core of the platform is built on a modern technology stack, featuring Next.js for the frontend, Supabase for authentication and database management, and Google's Gemini AI for intelligent data extraction from policy documents.

## 🚀 Key Features

*   **🤖 AI-Powered Onboarding**: Intelligent policy data extraction using Google Gemini API
*   **📋 Data Validation**: Comprehensive validation of extracted policy information
*   **📄 Policy Management**: Complete policy lifecycle management with status tracking
*   **📁 Document Management**: Secure file upload, storage, and download using Cloudflare R2
*   **🏛️ Insurance Auctions**: Reverse auction system where brokers compete for policies
*   **🔐 Secure Authentication**: Role-based access control with Supabase Auth and comprehensive RLS policies
*   **📊 User Dashboards**: Tailored interfaces for account holders, brokers, and administrators
*   **📱 Mobile Responsive**: Optimized mobile experience with consistent UI/UX across all pages
*   **⚡ Scalable Backend**: Built on Next.js 15+ with TypeScript and Prisma ORM
*   **☁️ Cloud Storage**: Cloudflare R2 for cost-effective, globally distributed file storage

## Architecture Overview

The application is architected as a **Role-Based Monolith** with a **100% Screaming Architecture** compliance. This design organizes the entire codebase around user roles (`ACCOUNT_HOLDER`, `BROKER`, `ADMIN`) and business domains (e.g., `policies`, `auctions`), rather than technical layers. The frontend is a Next.js 15+ application, backend logic is handled by Next.js API Routes, and the database is a PostgreSQL instance managed by Supabase, with Prisma as the ORM.

A critical and non-negotiable rule is that **all database operations must be executed server-side** via API Routes to enforce security and maintain a clean separation of concerns. Direct database access from the client is strictly forbidden.

## Business Logic

### Auction Duration Calculation

Auctions in Zeeguros follow a sophisticated working hours business logic to ensure fair and consistent timing across all insurance policy auctions:

#### Working Hours Configuration
- **Working Days**: Monday through Friday only
- **Working Hours**: 06:00 to 23:59 (Madrid timezone)
- **Daily Working Hours**: 18 hours per day (06:00:00 to 23:59:59)
- **Total Auction Duration**: 48 working hours (~2.67 business days)

#### Calculation Rules
1. **Weekend Exclusion**: Saturday and Sunday hours are completely excluded from duration calculations
2. **Pre-Working Hours**: Auctions starting before 06:00 are automatically moved to 06:00 of the same day
3. **Weekend Start**: Auctions starting on weekends are moved to Monday 06:00
4. **Timezone Handling**: All calculations use Europe/Madrid timezone for consistency
5. **End Date Calculation**: The `endDate` field is calculated using the working hours logic and represents when auctions actually close

#### Implementation
- **Core Logic**: Located in `src/lib/auction/working-hours.ts`
- **Key Function**: `calculateWorkingHoursClosedAt(startDate, 48)`
- **Database Field**: Single `endDate` field (consolidated from previous dual-field approach)
- **Automation**: Supabase cron jobs automatically close expired auctions every 5 minutes

#### Examples
- **Monday 10:00 start** → Closes Wednesday 22:00 (14h + 18h + 16h = 48h)
- **Friday 18:00 start** → Closes Wednesday 12:00 (6h + skip weekend + 18h + 18h + 6h = 48h)
- **Saturday start** → Moved to Monday 06:00, closes Wednesday 18:00 (18h + 18h + 12h = 48h)

### 📚 **Comprehensive Documentation**

This project maintains extensive documentation to support development and architectural decisions:

- **[Main Architecture Document](docs/architecture.md)**: Complete architectural blueprint and technical specifications
- **[Architecture Details](docs/architecture/)**: Detailed architectural documentation including:
  - Enhancement scope and tech stack alignment
  - Data models and component architecture
  - API design and external integrations
  - Infrastructure, security, and testing strategies
- **[Development Plans](docs/plans/)**: Current development roadmaps and PRDs
  - [Account Holder Journey Enhancement PRD](docs/plans/account-holder-journey-enhancement-prd.md): Active development plan for policy management completion
- **[Change History](docs/changelog/)**: Comprehensive version history and architectural evolution
- **[User Stories](docs/stories/)**: BMAD-driven development stories and requirements

### Architectural Philosophy: Screaming Architecture

This project strictly follows the principles of **Screaming Architecture**. The core idea is that the application's structure should "scream" its business domain, not the framework it uses.

**Our Implementation:**

*   **Domain-Driven Structure:** Instead of organizing code by technical type (e.g., `/components`, `/services`), we organize it by business domain within the `src/features` directory.
*   **User Roles as Domains:** For this platform, the primary business domains are the core user roles: `account-holder`, `broker`, and `admin`. This makes the codebase a direct reflection of our three-sided marketplace.
*   **Benefits:** This approach leads to high cohesion (related code lives together) and low coupling (domains are independent). It makes the system easier to understand, maintain, and scale, preventing the "slop code" that plagues less structured projects.

All new development must adhere to this feature-based structure. We also strictly follow the **Don't Repeat Yourself (DRY)** principle, ensuring that redundant code is eliminated and logic is consolidated into single, authoritative sources.
### 💠 Entity-Relationship Diagram (ERD)

This diagram illustrates the relationships between the core entities in the Zeeguros database.

```mermaid
erDiagram
    User {
        UUID id PK
        String email
        String phone
        String firstName
        String lastName
        String displayName
        Role role
        DateTime createdAt
        DateTime updatedAt
    }

    AccountHolderProfile {
        UUID id PK
        UUID userId FK
        DateTime createdAt
        DateTime updatedAt
    }

    BrokerProfile {
        UUID id PK
        UUID userId FK
        String registrationNumber
        String companyName
        InsurerCompany insurerCompany
        Boolean isActive
        Boolean isVerified
        Boolean canReceiveLeads
        KYCStatus kycStatus
        String stripeCustomerId
        DateTime createdAt
        DateTime updatedAt
    }

    AdminProfile {
        UUID id PK
        UUID userId FK
        DateTime createdAt
        DateTime updatedAt
    }

    Policy {
        UUID id PK
        UUID accountHolderId FK
        UUID assetId FK
        UUID documentId FK
        String policyNumber
        InsurerCompany insurer
        PolicyStatus status
        PolicyType type
        DateTime startDate
        DateTime endDate
        Decimal annualPremium
        PaymentPeriod paymentPeriod
        Boolean termsAccepted
        DateTime createdAt
        DateTime updatedAt
    }

    Asset {
        UUID id PK
        UUID accountHolderId FK
        AssetType type
        String name
        String description
        DateTime createdAt
        DateTime updatedAt
    }

    Vehicle {
        UUID id PK
        UUID assetId FK
        String make
        String model
        Int year
        String licensePlate
        FuelType fuelType
        GarageType garageType
        UsageType usageType
        KmRange kmRange
        DateTime createdAt
        DateTime updatedAt
    }

    InsuredParty {
        UUID id PK
        UUID accountHolderId FK
        PartyRole role
        String firstName
        String lastName
        String documentNumber
        Gender gender
        DateTime birthDate
        DateTime createdAt
        DateTime updatedAt
    }

    Coverage {
        UUID id PK
        UUID policyId FK
        GuaranteeType type
        String customName
        Decimal limit
        Decimal deductible
        String description
    }

    Auction {
        UUID id PK
        UUID accountHolderId FK
        UUID policyId FK
        DateTime startDate
        DateTime endDate
        DateTime workingHoursClosedAt
        AuctionState status
        Int maxWinners
        Int minWinners
        String cancellationReason
        DateTime createdAt
        DateTime updatedAt
    }

    Bid {
        UUID id PK
        UUID auctionId FK
        UUID brokerId FK
        UUID documentId FK
        Decimal amount
        DateTime createdAt
    }

    AuctionWinner {
        UUID id PK
        UUID auctionId FK
        UUID brokerId FK
        UUID bidId FK
        Int position
        DateTime selectedAt
        DateTime contactDataRevealedAt
    }

    AuctionCommission {
        UUID id PK
        UUID auctionId FK
        UUID winnerId FK
        UUID brokerId FK
        Decimal amount
        String status
        String stripePaymentIntentId
        DateTime paidAt
        DateTime createdAt
        DateTime updatedAt
    }

    Documentation {
        UUID id PK
        UUID accountHolderId FK
        UUID brokerId FK
        UUID auctionId FK
        UUID policyId FK
        String fileName
        String fileUrl
        String mimeType
        Int fileSize
        DocumentType type
        DateTime createdAt
        DateTime updatedAt
    }

    Address {
        UUID id PK
        UUID brokerId FK
        UUID insuredPartyId FK
        String street
        String city
        String postalCode
        Province province
        Region region
        Country country
        DateTime createdAt
        DateTime updatedAt
    }

    Subscription {
        UUID id PK
        UUID brokerId FK
        String stripeSubscriptionId
        String status
        DateTime currentPeriodEnd
        DateTime createdAt
        DateTime updatedAt
    }

    %% User Profile Relations
    User ||--o| AccountHolderProfile : "has"
    User ||--o| BrokerProfile : "has"
    User ||--o| AdminProfile : "has"

    %% Policy Relations
    AccountHolderProfile ||--|{ Policy : "owns"
    Asset ||--o| Policy : "covered by"
    Policy ||--|{ Coverage : "includes"
    Policy ||--o| Auction : "can have"
    Documentation ||--o{ Policy : "attached to"

    %% Asset Relations
    AccountHolderProfile ||--|{ Asset : "owns"
    Asset ||--o| Vehicle : "details"

    %% Insured Party Relations
    AccountHolderProfile ||--|{ InsuredParty : "includes"
    InsuredParty ||--o{ Address : "lives at"
    Policy ||--|{ PolicyInsuredParty : "covers"
    InsuredParty ||--|{ PolicyInsuredParty : "covered by"

    %% Auction Relations
    AccountHolderProfile ||--|{ Auction : "creates"
    Auction ||--|{ Bid : "receives"
    BrokerProfile ||--|{ Bid : "places"
    Auction ||--|{ AuctionWinner : "has"
    AuctionWinner ||--o| AuctionCommission : "pays"

    %% Broker Relations
    BrokerProfile ||--o| Subscription : "has"
    BrokerProfile ||--o{ Address : "located at"
    BrokerProfile ||--|{ AuctionWinner : "can be"
    BrokerProfile ||--|{ AuctionCommission : "pays"

    %% Documentation Relations
    AccountHolderProfile ||--|{ Documentation : "uploads"
    BrokerProfile ||--|{ Documentation : "uploads"
    Auction ||--|{ Documentation : "includes"
    Bid ||--o| Documentation : "attached to"

```

### 🧸 Understanding DRY and Screaming Architecture (Beginner-Friendly)

**Think of your codebase like organizing a toy box:**

#### **DRY Principle (Don't Repeat Yourself)**
```
🎯 GOOD: All auth toys in ONE box
├── actions/     ← "How to log in" toys
├── components/  ← "Login form" toys  
├── config/      ← "User rules" toys
├── hooks/       ← "Check if logged in" toys
├── services/    ← "Talk to server" toys
└── utils/       ← "Helper" toys
```

**Why it's DRY:** If you need ANYTHING about authentication, you only look in ONE place. No hunting around!

#### **Screaming Architecture**
When you see `src/features/auth`, you immediately know:
- **"This is about LOGIN/LOGOUT stuff!"** 
- It SCREAMS its business purpose
- Not technical stuff like "database" or "components" - it says "AUTH"!

#### **The Simple Rule**
- **If EVERYONE uses it the same way** → Put it in ONE shared place ✅
- **If DIFFERENT PEOPLE use it differently** → Split it by WHO uses it ✅

**Examples:**

**Authentication = Shared Infrastructure** ✅
- **Everyone** needs to login (account holders, brokers, admins)
- **Same process** for everyone
- **One central place** makes sense → `src/features/auth`

**Auctions = Role-Specific Features** ✅
- **Account holders** want to see THEIR auctions and pick winners
- **Brokers** want to browse available auctions and place bids
- **Admins** want to manage ALL auctions
- **Different needs** = should be in different places by role

Auth is like a shared door everyone uses, auctions are like different rooms where different people do different activities!

### ✅ **Current Architecture Status: 100% Screaming Architecture Compliance Achieved**

As of August 13, 2025, the platform has successfully completed comprehensive architectural transformation, achieving **100% screaming architecture compliance** with complete role-based organization and clean component structure.

#### **✅ ARCHITECTURAL ACHIEVEMENTS:**
*   **✅ Complete Role-Based Organization:** All routes organized by user roles (ADMIN, BROKER, ACCOUNT_HOLDER)
*   **✅ Technical Domain Elimination:** All technical domains removed from codebase
*   **✅ Business Domain Visibility:** Code structure clearly reflects business domains
*   **✅ DRY Principle Compliance:** Single PrismaClient usage, no duplicate logic
*   **✅ TypeScript A+ Compliance:** Full build passes without `ignoreBuildErrors`
*   **✅ Clean Build Status:** 29/29 pages generated successfully, zero errors
*   **✅ Pure Infrastructure Layer:** `src/lib` contains only generic, infrastructure-level components

#### **✅ RESOLVED ARCHITECTURAL VIOLATIONS:**
*   **✅ `src/app/auctions/`:** ~~Technical domain~~ → **Converted to role-based routes**
*   **✅ `src/features/auctions/`:** ~~Technical domain~~ → **Redistributed to proper feature domains**
*   **✅ `src/app/policies/`:** ~~Technical domain~~ → **Converted to role-based routes**
*   **✅ `src/app/settings/`:** ~~Technical domain~~ → **Converted to role-based routes**
*   **✅ `src/app/support/`:** ~~Technical domain~~ → **Converted to role-based routes**
*   **✅ `src/app/(dashboard)/`:** ~~Technical grouping~~ → **Eliminated completely**
*   **✅ Navigation System:** ~~Hardcoded technical domains~~ → **Dynamic role-based navigation**

#### **📊 Compliance Metrics:**
- **Feature Separation:** ✅ **100% (perfect domain organization)**
- **Component Organization:** ✅ **100% (clean UI/Shared/Domain separation)**
- **Route Delegation:** ✅ **100% (complete role-based routing)**
- **Build Quality:** ✅ **100% (zero errors, TypeScript A+)**
- **Overall Architecture:** ✅ **100% - SCREAMING ARCHITECTURE ACHIEVED**

**✅ CURRENT SCREAMING ARCHITECTURE STRUCTURE:**
```
src/
├── app/                       # ✅ Pure role-based organization
│   ├── (public)/              # ✅ Public routes (auth, login, signup)
│   ├── admin/                 # ✅ Role: Admin
│   ├── broker/                # ✅ Role: Broker
│   ├── account-holder/        # ✅ Role: Account Holder
│   └── api/                   # ✅ Server-side API routes
│       ├── documents/         # ✅ Document management endpoints
│       └── policies/          # ✅ Policy management endpoints
├── components/
│   ├── shared/                # ✅ App-wide, non-domain components
│   └── ui/                    # ✅ Generic shadcn/ui components
├── features/                  # ✅ Business domain features
│   ├── account-holder/        # ✅ Domain: Account Holder features
│   ├── admin/                 # ✅ Domain: Admin features
│   ├── auctions/              # ✅ Domain: Auction logic & components
│   ├── auth/                  # ✅ Domain: Auth logic (server)
│   ├── broker/                # ✅ Domain: Broker features
│   └── policies/              # ✅ Domain: Policy logic & components
└── lib/                       # ✅ Pure infrastructure layer
    ├── db.ts                  # ✅ Database client singleton
    ├── r2.ts                  # ✅ Cloudflare R2 storage client
    ├── zod/                   # ✅ Zod schemas (auto-generated)
    └── utils.ts               # ✅ Generic utilities only
```

**🏆 ARCHITECTURAL VICTORY ACHIEVED:** The platform now represents a **gold standard** of screaming architecture implementation with complete business domain visibility and zero technical debt.

### 📄 **Document Management System (August 2025)**

The platform includes a comprehensive document management system with the following capabilities:

#### **✅ Implemented Features:**
- **Secure File Upload**: Policy documents uploaded to Cloudflare R2 storage during policy creation
- **Document Integration**: Policy documents linked to policies through the `Documentation` model
- **Download Functionality**: Secure document download with authentication and access control
- **UI Integration**: Document section in Policy Details drawer with file metadata display
- **Storage Architecture**: Cloudflare R2 for cost-effective, globally distributed file storage

#### **🔐 Security Features:**
- **Authentication Required**: All document operations require valid user session
- **Access Control**: Users can only access documents they own
- **Server-Side Processing**: All file operations handled through secure API routes
- **Row-Level Security**: Comprehensive RLS policies implemented for all profile tables
- **User Profile Sync**: Fixed trigger functions for automatic profile creation during signup

#### **🏗️ Technical Implementation:**
- **Storage Provider**: Cloudflare R2 with S3-compatible API using AWS SDK v3
- **File Processing**: Server-side file handling with proper MIME type detection
- **Database Integration**: Document metadata stored in PostgreSQL via Prisma
- **API Architecture**: RESTful endpoints for upload, download, and metadata retrieval
- **Security Definer Functions**: Enhanced trigger functions with proper error handling and validation

## 📋 Recent Updates (August 2025)

### 🐛 **Latest Bug Fixes & Improvements (August 13, 2025)**

- **🔧 Database Security**: Fixed Supabase user profile sync trigger failures and implemented comprehensive RLS policies
- **📊 Data Accuracy**: Resolved auction counting discrepancies where KPI totals exceeded actual auction counts
- **🔍 Filter Functionality**: Fixed asset type filter buttons by restructuring API query construction
- **📱 Mobile UX**: Enhanced mobile responsiveness for policy and auction lists with optimized components
- **🎨 UI Consistency**: Implemented consistent sticky headers with integrated sidebar toggles across all pages
- **📄 Policy Status**: Added "Póliza firmada" KPI card and improved layout standardization

#### **📋 Recent Architectural Improvements (August 2025)**

- **✅ UI/UX Consistency**: Implemented consistent sticky headers with integrated sidebar toggles across all account-holder pages
- **✅ Mobile Responsiveness**: Enhanced mobile experience for policy and auction lists with optimized KPI cards, filter buttons, and pagination
- **✅ Security Hardening**: Fixed Supabase user profile sync triggers and implemented comprehensive RLS policies for all profile tables
- **✅ Data Accuracy**: Resolved auction counting discrepancies and fixed asset type filtering functionality
- **✅ Layout Standardization**: Unified layout structure between "Mis Pólizas" and "Mis Subastas" pages for consistent user experience
- **✅ Component Organization**: Moved broker-specific Kanban components to proper domain boundaries

For detailed change history, see the [August 2025 changelog](docs/changelog/2025-08/) and the [comprehensive architecture document](docs/architecture.md).

### 🏛️ System Architecture Diagram

This diagram provides a detailed view of the **Role-Based Monolith** architecture, illustrating the flow of requests and the separation of concerns between the client, server, and external services.

```mermaid

graph TD
    subgraph Browser["User's Browser"]
        A[Next.js Client Components]
        B[Role-Based Routes]
    end

    subgraph Server["Zeeguros Server - OCI ARM Ampere VPS"]
        C[Next.js API Routes]
        D[Prisma Client]
        E[Google Gemini API]
        F[Brevo API]
    end

    subgraph Infrastructure["Infrastructure - Supabase"]
        G[PostgreSQL Database]
        H[Supabase Auth]
        I[JWT Validation]
    end

    A -->|User Interaction| B
    B -->|API Request| C
    C -->|DB Operations| D
    C -->|AI Processing| E
    C -->|Email Notifications| F
    D -->|SQL Query| G
    
    H -->|Validates JWT| I
    I -->|Auth Check| C
    
    G -.->|RLS Policies| D

    classDef clientStyle fill:#cce5ff,stroke:#333,stroke-width:2px
    classDef serverStyle fill:#d4f0f0,stroke:#333,stroke-width:2px
    classDef infraStyle fill:#FDEDEC,stroke:#333,stroke-width:2px
    classDef authStyle fill:#e6e6fa,stroke:#333,stroke-width:2px
    
    class A,B clientStyle
    class C,D,E,F serverStyle
    class G infraStyle
    class H,I authStyle
```

### AI Data Extraction Flow

The following diagram illustrates how policy data is extracted, validated, and transformed.

```mermaid
sequenceDiagram
    participant User
    participant Frontend as Next.js Frontend
    participant API as /api/policies/extract
    participant Gemini as Google Gemini API
    participant Bodyguard as Bodyguard Validator
    participant Validator as Zod Schema Validator

    User->>Frontend: Uploads policy document (PDF, JPG, PNG)
    Frontend->>API: POST request with file data
    API->>Gemini: Sends document and prompt for data extraction
    Gemini-->>API: Returns structured JSON data
    
    API->>Bodyguard: Pre-validates raw JSON response
    alt Bodyguard Validation Fails
        Bodyguard-->>API: Returns security/validation error
        API-->>Frontend: Sends 400 Bad Request with error
        Frontend->>User: Displays generic error message
    else Bodyguard Validation Succeeds
        Bodyguard-->>API: Returns sanitized data
        API->>Validator: Transforms and validates with Zod schema
        alt Zod Validation Fails
            Validator-->>API: Returns detailed validation errors
            API-->>Frontend: Sends 400 Bad Request with error details
            Frontend->>User: Displays detailed error message
        else Zod Validation Succeeds
            Validator-->>API: Returns validated data
            API-->>Frontend: Sends 200 OK with extracted JSON data
            Frontend->>User: Displays extracted data in form
        end
    end
```



### 🛠️ Technology Stack

#### **Frontend**
*   **Next.js 15+**: React framework with App Router
*   **TypeScript**: Type-safe development (100% coverage)
*   **Tailwind CSS**: Utility-first CSS framework
*   **Radix UI**: Accessible component primitives
*   **React Hook Form**: Form management with validation
*   **Zod**: Schema validation
*   **Lucide React**: Modern icon library

#### **Backend**
*   **Next.js API Routes**: Server-side API endpoints
*   **Prisma ORM**: Database toolkit and query builder
*   **PostgreSQL**: Primary database (via Supabase)
*   **Supabase**: Authentication, database hosting, and Row-Level Security
*   **AWS SDK v3**: S3-compatible operations for Cloudflare R2

#### **AI & External Services**
*   **Google Gemini API**: AI-powered document analysis
*   **Brevo**: Email service provider for notifications
*   **Cloudflare R2**: Object storage for documents

#### **Development Tools**
*   **ESLint**: Code linting with custom rules
*   **Prettier**: Code formatting
*   **TypeScript**: Static type checking
*   **Prisma Studio**: Database management interface

## Database Schema

The database schema is defined and managed using Prisma. Here are the core models:

*   **`User`**: Stores user authentication information and role (`ACCOUNT_HOLDER`, `BROKER`, or `ADMIN`).
*   **`AccountHolderProfile` / `BrokerProfile` / `AdminProfile`**: Store role-specific data, linked to a `User`.
*   **`Policy`**: Contains all details of an insurance policy, linking together the customer, broker, asset, and coverages.
*   **`Asset`**: Stores detailed information about the insured asset.
*   **`InsuredParty`**: Represents individuals covered by a policy (e.g., policyholder, owner, driver) and links to a generic `Person` model.
*   **`Coverage`**: Defines the specific guarantees and financial details (limits, deductibles) of a policy.
*   **`Auction`**: Manages the insurance auction data, linked to a user and a asset.
*   **`AuctionBid`**: Stores bids made by brokers on auctions.

For the complete and detailed schema, please see [`prisma/schema.prisma`](prisma/schema.prisma).

## Prerequisites

Before you begin, ensure you have the following installed:

*   **Node.js** (v18.17.0 or higher)
*   **npm** or your preferred package manager
*   **Git**
*   A [Supabase](https://supabase.com/) project for database and authentication
*   A [Google Gemini API Key](https://aistudio.google.com/app/apikey)
*   **Cloudflare R2** account for document storage

## Installation and Setup

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/your-username/zee-next-app.git
    cd zee-next-app
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    ```

3.  **Set up environment variables:**
    Create a `.env.local` file by copying the example file:
    ```bash
    cp .env.example .env.local
    ```
    Then, fill in the required values in `.env.local`:
    *   `NEXT_PUBLIC_SITE_URL`: The public URL of your application (e.g., `http://localhost:3000`)
    *   `DATABASE_URL`: Your Supabase database connection string (with pooling)
    *   `DIRECT_URL`: Your Supabase direct database connection string (for migrations)
    *   `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
    *   `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
    *   `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key
    *   `SUPABASE_ACCESS_TOKEN`: Your Supabase access token
    *   `GEMINI_API_KEY`: Your Google Gemini API key
    *   `GEMINI_MODEL`: The Gemini model to use (e.g., `gemini-2.5-flash-lite-preview-06-17`)
    *   `BREVO_API_KEY`: Your Brevo API key for email notifications
    *   `R2_ACCOUNT_ID`: Your Cloudflare R2 Account ID
    *   `R2_ACCESS_KEY_ID`: Your Cloudflare R2 Access Key ID
    *   `R2_SECRET_ACCESS_KEY`: Your Cloudflare R2 Secret Access Key
    *   `R2_BUCKET_NAME`: Your Cloudflare R2 bucket name

4.  **Run database migrations:**
    ```bash
    npx prisma migrate dev
    ```

5.  **Seed the database:**
    ```bash
    npm run db:seed
    ```
    
    **Note**: If you encounter issues with the `SIGNED_POLICY` status during seeding, this is a known issue documented in the [August 11, 2025 changelog](docs/changelog/2025-08/2025-08-11-changelog-42.md).

## Getting Started

To get a local copy up and running, follow these simple steps.

### Database Setup

If you are starting from a clean database, or if you need to reset your local environment, run the following commands in order:

1.  **Remove existing migrations:**
    ```bash
    rm -rf prisma/migrations
    ```
2.  **Reset the database:** This command will drop the database, apply all migrations, and ensure your schema is up to date.
    ```bash
    npm run migrate:reset
    ```

3.  **Create the first migration:**
    ```bash
    npm run migrate:dev -- --name "first_schema"
    ```

4.  **Seed the database:** This will populate the database with essential test data, including user accounts for each role.
    ```bash
    npm run db:seed
    ```

### Complete Database Rebuild

For a **complete database rebuild from scratch** (recommended for development and testing), use the master rebuild command:

```bash
npm run db:rebuild
```

This single command performs the following operations in sequence:

1. **`npm run migrate:reset -- --force`**: Drops all data and resets Prisma migrations
2. **`npm run db:setup-infrastructure`**: Sets up Supabase infrastructure:
   - **Extensions**: Enables `pg_cron` and `pg_net` for automation
   - **Functions**: Creates notification logging, statistics, and auction management functions
   - **Cron Jobs**: Schedules auction expiration and notification jobs (every 5 minutes)
3. **`npm run db:setup-migrations`**: Applies additional SQL migrations:
   - **Auction Expiration Logic**: Timezone-aware auction closure with working hours support
   - **Manual Functions**: Testing and emergency auction closure functions
4. **`npm run db:setup-config`**: Configures database settings (may require manual setup)
5. **`npm run db:deploy-functions`**: Deploys Supabase Edge Functions (requires Docker)
6. **`npm run db:apply-policies`**: Applies Row-Level Security (RLS) policies
7. **`npm run db:seed`**: Populates database with comprehensive test data

**Use this command when:**
- Setting up a new development environment
- Resetting your local database completely
- Testing database schema changes
- Preparing for deployment testing

**Note**: The Edge Function deployment step requires Docker to be running. If Docker is not available, the other steps will still complete successfully.

### Zod Schema Generation

If you encounter issues with generated Zod schemas, you can regenerate them with the following commands:

```bash
rm -rf src/lib/zod
npx prisma generate
```

### Test User Credentials

The seed script creates the following test users:

*   **Account Holder:** `<EMAIL>` / `Abcdef7*`
*   **Broker:** `<EMAIL>` / `Abcdef7*`
*   **Admin:** `<EMAIL>` / `Abcdef7*`

## Running the Project

To run the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:3000`.

### Available Scripts

*   `npm run dev`: Starts the development server with Turbopack
*   `npm run build`: Builds the application for production
*   `npm run start`: Starts a production server
*   `npm run lint`: Lints the codebase with custom ESLint rules
*   `npm run migrate:dev`: Runs database migrations for development
*   `npm run db:seed`: Seeds the database with comprehensive test data
*   `npm run db:studio`: Opens Prisma Studio to view and manage your data
*   `npm run db:rebuild`: **Complete database rebuild from scratch** - Resets database, applies Prisma migrations, sets up infrastructure (extensions, functions, cron jobs), deploys Edge Functions, applies RLS policies, and seeds comprehensive test data
*   `npm run db:migrate`: Runs database migrations

## API Reference

The main API endpoints are defined under `src/app/api`:

*   **`POST /api/policies/extract`**: The endpoint for AI-powered data extraction. It accepts a file (PDF, JPG, PNG) and returns structured JSON data.
*   **`POST /api/policies/create`**: Creates new policy records with AI-extracted data and document storage.
*   **`GET /api/documents/download`**: Secure document download endpoint with authentication and access control.
*   **`GET /api/account-holder/policies/list`**: Retrieves policy list with document metadata for account holders.
*   **Server Actions:** The application is moving towards a server-action-centric architecture. Core business logic for policies and auctions will be handled via secure, server-side actions and not traditional REST endpoints.

### **📚 Comprehensive API Documentation**

For detailed API design patterns, security requirements, and integration guidelines, see:

- **[API Design and Integration](docs/architecture/6-api-design-and-integration.md)**: Complete API architecture patterns and security requirements
- **[External API Integration](docs/architecture/7-external-api-integration.md)**: Google Gemini AI, Brevo SMTP, and Cloudflare R2 integration patterns
- **[Security Integration](docs/architecture/12-security-integration.md)**: Server-side security patterns and authentication requirements
- **[Main Architecture Document](docs/architecture.md)**: Complete technical specifications including API design principles

## Contributing

Contributions are welcome! Please follow the standard GitHub flow:

1.  Fork the repository.
2.  Create a new branch (`git checkout -b feature/your-feature`).
3.  Make your changes and commit them (`git commit -m 'Add some feature'`).
4.  Push to the branch (`git push origin feature/your-feature`).
5.  Open a Pull Request.

### Development Guidelines

*   **Follow Screaming Architecture:** All new features must be organized by business domain in `src/features/`
*   **Respect Component Boundaries:** Use `src/components/ui/` for generic components only, `src/components/shared/` for app-wide components
*   **Maintain Code Quality:** All code must pass TypeScript compilation without `ignoreBuildErrors`
*   **DRY Principle:** Avoid duplicate logic, use existing services and utilities
*   **Documentation:** Update relevant documentation in `docs/` for architectural changes

#### **📖 Development Resources**

- **[Main Architecture Document](docs/architecture.md)**: Complete architectural standards and patterns
- **[Current Development Plan](docs/plans/account-holder-journey-enhancement-prd.md)**: Active PRD for account holder journey completion
- **[Latest Changes](docs/changelog/2025-07/)**: Recent architectural improvements and component relocations
- **[Architecture Details](docs/architecture/)**: Comprehensive technical specifications and integration guidelines

#### **🎯 Current Development Focus**

The platform is currently focused on completing the **Account Holder Journey Enhancement** as outlined in the [active PRD](docs/plans/account-holder-policies-prd.md), which includes:

- **✅ R2 Storage Migration**: Complete transition from Supabase storage to Cloudflare R2
- **✅ Document Management**: Secure file upload, storage, and download functionality implemented
- **✅ Server-Side Security**: All database operations through authenticated API routes with comprehensive RLS policies
- **✅ UI/UX Consistency**: Unified layout and mobile responsiveness across all account-holder pages
- **✅ Data Accuracy**: Fixed auction counting and filtering functionality
- **✅ Database Security**: Fixed user profile sync triggers and implemented comprehensive RLS policies
- **⏳ Policy Lifecycle Management**: Complete DRAFT → ACTIVE → RENEW_SOON → EXPIRED workflow
- **⏳ Brevo SMTP Integration**: Email notifications for policy status changes
- **⏳ Enhanced User Experience**: Advanced policy management and broker interaction features

#### **🐛 Known Issues**

- **Database Seeding**: `SIGNED_POLICY` status may cause issues during seeding (documented in [changelog](docs/changelog/2025-08/2025-08-11-changelog-42.md))
- **Policy-Auction Linking**: Integration between `RENEW_SOON` policies and auction system is in development

## 📈 Project Status & Roadmap

### **Current Status (August 2025)**

Zeeguros is in active development with a focus on completing the Account Holder Journey Enhancement. The platform has achieved significant milestones:

- **✅ Core Infrastructure**: Fully implemented with Next.js 15+, TypeScript, and Prisma
- **✅ Authentication & Security**: Complete role-based access control with comprehensive RLS policies
- **✅ Document Management**: Secure file handling with Cloudflare R2 integration
- **✅ AI Integration**: Google Gemini API for intelligent policy data extraction
- **✅ Mobile Responsiveness**: Optimized UI/UX across all device sizes
- **✅ Database Architecture**: Robust schema with proper relationships and constraints
- **✅ Policy Lifecycle**: Complete workflow from DRAFT to EXPIRED status

### **Upcoming Features**

- **📧 Email Notifications**: Brevo SMTP integration for policy status updates
- **🔗 Auction Integration**: Enhanced linking between RENEW_SOON policies and auctions
- **💰Stripe Payment Integration**: Secure payment processing for broker bidding
- **📊 Advanced Analytics**: Comprehensive reporting and insights dashboard
- **🌐 Multi-language Support**: Expanded localization beyond Spanish

### **Documentation**

For detailed technical requirements and implementation guidelines, see the comprehensive documentation:

- **[Architecture Overview](docs/architecture.md)**: Complete system architecture and design principles
- **[Product Requirements](docs/plans/account-holder-policies-prd.md)**: Current development focus and requirements
- **[Change History](docs/changelog/)**: Detailed changelog organized by month
- **[API Documentation](docs/api/)**: Complete API reference and examples

---

**Built with ❤️ by the Zeeguros team** | **Last updated: August 13, 2025**

## License

All rights reserved by ZEEGUROS PLATFORM S.L.
