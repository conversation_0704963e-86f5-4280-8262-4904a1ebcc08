"use client";

import { useEffect, useState, ChangeEvent } from "react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { PrimaryButton } from "@/components/shared/primary-button";
import { Plus, Search, X, Loader2, AlertCircle, FileText, ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";
import { AuctionSummaryCard } from "@/features/auctions/components/auction-summary-card";
import { useAuctions, useAuctionCounts } from "../hooks/useAuctions";
import { AuctionState } from "@prisma/client";

const auctionStatusOptions: { value: AuctionState | "all"; label: string }[] = [
  { value: "all", label: "Todas" },
  { value: "OPEN", label: "Subastas abiertas" },
  { value: "CLOSED", label: "Subastas cerradas" },
  { value: "SIGNED_POLICY", label: "Póliza firmada" },
  { value: "CANCELED", label: "Canceladas" },
  { value: "EXPIRED", label: "Expiradas" },
];

function LoadingState() {
  return (
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
        <p className="text-gray-600">Cargando subastas...</p>
      </div>
    </div>
  );
}

function ErrorState({ error, onRetry }: { error: string; onRetry: () => void }) {
  return (
    <Card>
      <CardContent className="p-10 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error al cargar subastas</h3>
        <p className="text-gray-600 mb-6">{error}</p>
        <Button onClick={onRetry} variant="outline">
          Intentar de nuevo
        </Button>
      </CardContent>
    </Card>
  );
}

export function AuctionList() {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(4);

  const [statusFilter, setStatusFilter] = useState<AuctionState | "all">("all");
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setDebouncedSearchTerm(searchTerm), 800); // 800ms delay - improved UX for typing accented characters
    return () => clearTimeout(timer);
  }, [searchTerm]);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const { data, isLoading, error } = useAuctions({
    page: currentPage,
    limit: itemsPerPage,
    status: statusFilter !== "all" ? statusFilter : undefined,
    search: debouncedSearchTerm || undefined,
  });

  const { data: counts, isLoading: isLoadingCounts } = useAuctionCounts();

  const auctions = data?.data || [];
  const pagination = data?.pagination;

  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value);
  useEffect(() => { setCurrentPage(1); }, [debouncedSearchTerm]);


  const isInitialLoading = (isLoading && !data) || isLoadingCounts;
  const isSearching = isLoading && !!data && (searchTerm !== debouncedSearchTerm);

  if (isInitialLoading) {
    return (
      <div className="flex flex-1 flex-col">
        {/* Sticky Header */}
        <div className={`sticky top-0 z-10 bg-white py-4 ${isScrolled ? 'shadow-md' : ''} transition-shadow duration-200`}>
          <div className="px-4">
            {/* Title with Sidebar Trigger */}
            <div className="flex items-center gap-4 mb-4">
              <SidebarTrigger className="-ml-1 hover:bg-gray-100" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Mis Subastas</h1>
                <p className="text-gray-600">Gestiona tus subastas de seguros</p>
              </div>
            </div>
            <Separator className="mb-4" />
          </div>
        </div>
        {/* Content */}
        <div className="flex-1 px-3 sm:px-4 pb-4">
          <LoadingState />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-1 flex-col">
        {/* Sticky Header */}
        <div className={`sticky top-0 z-10 bg-white py-4 ${isScrolled ? 'shadow-md' : ''} transition-shadow duration-200`}>
          <div className="px-3 sm:px-4">
            {/* Title with Sidebar Trigger */}
            <div className="flex items-center gap-4 mb-4">
              <SidebarTrigger className="-ml-1 hover:bg-gray-100" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Mis Subastas</h1>
                <p className="text-gray-600">Gestiona tus subastas de seguros</p>
              </div>
            </div>
            <Separator className="mb-4" />
          </div>
        </div>
        {/* Content */}
        <div className="flex-1 px-3 sm:px-4 pb-4">
          <ErrorState error={error instanceof Error ? error.message : "Error desconocido"} onRetry={() => window.location.reload()} />
        </div>
      </div>
    );
  }

  // Function to get filter labels with dynamic counts
  const getFilterLabel = (status: AuctionState | "all") => {
    switch (status) {
      case "all":
        return `Todas las subastas (${counts?.total || 0})`;
      case "OPEN":
        return `Subastas abiertas (${counts?.open || 0})`;
      case "CLOSED":
        return `Subastas cerradas (${counts?.closed || 0})`;
      case "SIGNED_POLICY":
        return `Póliza firmada (${counts?.signedPolicy || 0})`;
      case "CANCELED":
        return `Canceladas (${counts?.canceled || 0})`;
      case "EXPIRED":
        return `Expiradas (${counts?.expired || 0})`;
      default:
        return `Todas las subastas (${counts?.total || 0})`;
    }
  };




  const handlePageChange = (page: number) => setCurrentPage(page);
  const handleItemsPerPageChange = (value: string) => { setItemsPerPage(Number(value)); setCurrentPage(1); };

  return (
    <div className="flex flex-1 flex-col">
      {/* Sticky Header */}
      <div className={`sticky top-0 z-10 bg-white py-4 ${isScrolled ? 'shadow-md' : ''} transition-shadow duration-200`}>
        <div className="px-3 sm:px-4">
          {/* Title with Sidebar Trigger */}
          <div className="flex items-center gap-4 mb-4">
            <SidebarTrigger className="-ml-1 hover:bg-gray-100" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Mis Subastas</h1>
              <p className="text-gray-600">Gestiona tus subastas de seguros</p>
            </div>
          </div>
          <Separator className="mb-3" />

          {/* KPI Cards */}
          <div className="mb-3">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              <Card className="border-l-4" style={{borderLeftColor: '#3AE386'}}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div className="min-w-0 flex-1 pr-2">
                      <h3 className="text-sm font-semibold text-gray-900">Abiertas</h3>
                      <p className="text-xs text-muted-foreground mt-0.5">Subastas en curso</p>
                    </div>
                    <p className="text-2xl font-bold flex-shrink-0" style={{color: '#3AE386'}}>{counts?.open || 0}</p>
                  </div>
                </CardContent>
              </Card>
              <Card className="border-l-4" style={{borderLeftColor: '#3EA050'}}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div className="min-w-0 flex-1 pr-2">
                      <h3 className="text-sm font-semibold text-gray-900">Cerradas</h3>
                      <p className="text-xs text-muted-foreground mt-0.5">Subastas finalizadas</p>
                    </div>
                    <p className="text-2xl font-bold flex-shrink-0" style={{color: '#3EA050'}}>{counts?.closed || 0}</p>
                  </div>
                </CardContent>
              </Card>
              <Card className="border-l-4" style={{borderLeftColor: '#000000'}}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-center">
                    <div className="min-w-0 flex-1 pr-2">
                      <h3 className="text-sm font-semibold text-gray-900">Póliza firmada</h3>
                      <p className="text-xs text-muted-foreground mt-0.5">Subastas con póliza firmada</p>
                    </div>
                    <p className="text-2xl font-bold flex-shrink-0" style={{color: '#000000'}}>{counts?.signedPolicy || 0}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Filter, Search and Create Button - Horizontal Layout */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Filter Selector */}
            <div className="flex items-center gap-2 sm:w-auto">
              <Select onValueChange={(v: any) => { setStatusFilter(v as any); setCurrentPage(1); }} value={statusFilter}>
                <SelectTrigger className="w-full sm:w-48 bg-white h-10">
                  <SelectValue placeholder={auctionStatusOptions.find(o => o.value === statusFilter)?.label} />
                </SelectTrigger>
                <SelectContent>
                  {auctionStatusOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {getFilterLabel(option.value)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {statusFilter !== "all" && (
                <Button
                  size="icon"
                  variant="outline"
                  onClick={() => { setStatusFilter("all"); setCurrentPage(1); }}
                  className="h-10 w-10 flex-shrink-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>

            {/* Search Bar */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar por identificador, marca, modelo o aseguradora..."
                className="pl-10 pr-10 h-10 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
              {isSearching && (
                <Loader2 className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground" />
              )}
            </div>

            {/* Create Button */}
            <div className="sm:w-auto">
              <Link href="/account-holder/policies/new-policy">
                <PrimaryButton className="gap-2 w-full sm:w-auto h-10 whitespace-nowrap">
                  <Plus className="h-4 w-4" />
                  Crear nueva subasta
                </PrimaryButton>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="px-3 sm:px-4 space-y-4">
        {auctions.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {auctions.map((a) => (
              <AuctionSummaryCard
                key={a.id}
                auction={a}
              />
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-10 text-center">
              <div className="text-gray-400 mb-4">
                <FileText className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No se encontraron subastas
              </h3>
              <p className="text-gray-600 mb-6">
                {debouncedSearchTerm || statusFilter !== "all"
                  ? "Intenta ajustar los filtros de búsqueda."
                  : "Comienza creando tu primera subasta."}
              </p>
              {(!debouncedSearchTerm && statusFilter === "all") && (
                <Link href="/account-holder/policies/new-policy">
                  <PrimaryButton className="gap-1">
                    <Plus className="h-4 w-4" />
                    Crear nueva subasta
                  </PrimaryButton>
                </Link>
              )}
            </CardContent>
          </Card>
        )}

        {/* Pagination Controls - Bottom */}
        {pagination && pagination.totalCount > 0 && (
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-4 pt-4 pb-8">
            <div className="flex items-center justify-center gap-1 sm:gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="bg-primary text-black hover:bg-primary text-xs sm:text-sm px-2 sm:px-3"
              >
                Anterior
              </Button>

              <span className="text-xs sm:text-sm text-gray-600 whitespace-nowrap px-2">
                Página {pagination.page} de {pagination.totalPages}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === pagination.totalPages}
                className="bg-primary text-black hover:bg-primary text-xs sm:text-sm px-2 sm:px-3"
              >
                Siguiente
              </Button>
            </div>

            <div className="flex items-center justify-center gap-1 sm:gap-2">
              <span className="text-xs sm:text-sm text-gray-600 whitespace-nowrap">Mostrar</span>
              <Select
                value={itemsPerPage.toString()}
                onValueChange={handleItemsPerPageChange}
              >
                <SelectTrigger className="w-16 sm:w-20 text-center text-xs sm:text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="4">4</SelectItem>
                  <SelectItem value="8">8</SelectItem>
                  <SelectItem value="12">12</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-xs sm:text-sm text-gray-600 whitespace-nowrap">elementos</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
