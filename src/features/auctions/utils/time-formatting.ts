/**
 * Utility functions for formatting auction time remaining
 */

import { calculateWorkingHoursBetween } from "@/lib/auction/working-hours";

/**
 * Calculates and formats the working time remaining until an auction ends
 * @param endDate - The auction end date (ISO string or Date)
 * @returns Formatted time string (e.g., "2d 5h", "3h 45m", "30m", "Finalizada")
 */
export function formatTimeRemaining(endDate: string | Date): string {
  const now = new Date();
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;

  // Check if the date is valid
  if (isNaN(end.getTime())) {
    return "Finalizada";
  }

  // If auction has ended
  if (end.getTime() <= now.getTime()) {
    return "Finalizada";
  }

  // Calculate working hours remaining (not calendar time)
  const workingHoursRemaining = calculateWorkingHoursBetween(now, end);

  // If no working hours remaining (e.g., weekend or after hours)
  if (workingHoursRemaining <= 0) {
    return "Finalizada";
  }

  // Convert working hours to days and hours
  const workingDays = Math.floor(workingHoursRemaining / 18); // 18 working hours per day
  const remainingHours = Math.floor(workingHoursRemaining % 18);
  const remainingMinutes = Math.floor((workingHoursRemaining % 1) * 60);

  // Format based on working time remaining
  if (workingDays > 0) {
    if (remainingHours > 0) {
      return `${workingDays}d ${remainingHours}h`;
    }
    return `${workingDays}d`;
  } else if (remainingHours > 0) {
    if (remainingMinutes > 0) {
      return `${remainingHours}h ${remainingMinutes}m`;
    }
    return `${remainingHours}h`;
  } else {
    return `${remainingMinutes}m`;
  }
}

/**
 * Gets the urgency level based on working time remaining
 * @param endDate - The auction end date (ISO string or Date)
 * @returns Urgency level: "high", "medium", "low", or "finished"
 */
export function getTimeUrgency(endDate: string | Date): "high" | "medium" | "low" | "finished" {
  const now = new Date();
  const end = typeof endDate === 'string' ? new Date(endDate) : endDate;

  if (isNaN(end.getTime())) {
    return "finished";
  }

  if (end.getTime() <= now.getTime()) {
    return "finished";
  }

  // Calculate working hours remaining (not calendar time)
  const workingHoursRemaining = calculateWorkingHoursBetween(now, end);

  if (workingHoursRemaining <= 0) {
    return "finished";
  }

  // Use working hours for urgency calculation
  if (workingHoursRemaining <= 2) {
    return "high";
  } else if (workingHoursRemaining <= 12) {
    return "medium";
  } else {
    return "low";
  }
}

/**
 * Gets the appropriate CSS classes for time remaining badge based on urgency
 * @param endDate - The auction end date (ISO string or Date)
 * @returns CSS classes for styling the time badge
 */
export function getTimeRemainingBadgeClasses(endDate: string | Date): string {
  const urgency = getTimeUrgency(endDate);
  
  switch (urgency) {
    case "high":
      return "bg-red-100 text-red-800 border-red-200";
    case "medium":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case "low":
      return "bg-green-100 text-green-800 border-green-200";
    case "finished":
      return "bg-gray-100 text-gray-800 border-gray-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
}
