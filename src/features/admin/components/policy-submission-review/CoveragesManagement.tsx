"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PlusCircle, Trash2, Edit } from "lucide-react";
import { Coverage, GuaranteeType } from "@/features/admin/hooks/usePolicySubmissionReview";
import { translateGuaranteeType } from "@/features/policies/utils/translations";
import { CoverageFormModal } from "./CoverageFormModal";

interface CoveragesManagementProps {
  coverages: Coverage[];
  onCoveragesChange: (coverages: Coverage[]) => void;
}

export function CoveragesManagement({
  coverages,
  onCoveragesChange,
}: CoveragesManagementProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCoverage, setEditingCoverage] = useState<Coverage | null>(null);

  const handleAddCoverage = () => {
    setEditingCoverage(null);
    setIsModalOpen(true);
  };

  const handleEditCoverage = (coverage: Coverage) => {
    setEditingCoverage(coverage);
    setIsModalOpen(true);
  };

  const handleDeleteCoverage = (coverageToDelete: Coverage) => {
    onCoveragesChange(coverages.filter((c) => c !== coverageToDelete));
  };

  const handleSaveCoverage = (coverage: Coverage) => {
    if (editingCoverage) {
      // Update existing coverage
      onCoveragesChange(
        coverages.map((c) => (c === editingCoverage ? coverage : c))
      );
    } else {
      // Add new coverage
      onCoveragesChange([...coverages, coverage]);
    }
  };

  const formatCurrency = (value?: number) => {
    if (value === undefined) return "N/A";
    return new Intl.NumberFormat("es-ES", {
      style: "currency",
      currency: "EUR",
    }).format(value);
  };

  return (
    <Card>
      <CardHeader className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <CardTitle>Gestión de Coberturas</CardTitle>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleAddCoverage}
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          Añadir cobertura
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {coverages.map((coverage, index) => (
            <div
              key={index}
              className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 rounded-md border"
            >
              <div className="flex-1">
                <p className="font-semibold">
                  {coverage.type === GuaranteeType.OTHER
                    ? coverage.customName
                    : translateGuaranteeType(coverage.type)}
                </p>
                <div className="flex space-x-4 text-sm text-muted-foreground">
                  <p>
                    <span className="font-medium">Límite:</span>{" "}
                    {formatCurrency(coverage.limit)}
                  </p>
                  <p>
                    <span className="font-medium">Franquicia:</span>{" "}
                    {formatCurrency(coverage.deductible)}
                  </p>
                </div>
                {coverage.description && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {coverage.description}
                  </p>
                )}
              </div>
              <div className="flex items-center space-x-2 self-end mt-2 sm:mt-0">
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => handleEditCoverage(coverage)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => handleDeleteCoverage(coverage)}
                  className="text-red-500 hover:text-red-600"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}
          {coverages.length === 0 && (
            <p className="text-center text-muted-foreground py-4">
              No hay coberturas añadidas.
            </p>
          )}
        </div>
      </CardContent>

      {isModalOpen && (
        <CoverageFormModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSave={handleSaveCoverage}
          coverage={editingCoverage}
        />
      )}
    </Card>
  );
}